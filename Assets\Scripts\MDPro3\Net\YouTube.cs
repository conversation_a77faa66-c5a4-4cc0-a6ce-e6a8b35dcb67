using System;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.Networking;
using System.Collections.Generic;
using System.Xml;
using MDPro3.Utility;

namespace MDPro3.Net
{
    public static class YouTube
    {
        // URL do RSS feed do canal Tenshimaru
        // Opção 1: Por username
        private const string channelRssUrl = "https://www.youtube.com/feeds/videos.xml?user=Tenshimaru";
        // Opção 2: Por Channel ID (substitua CHANNEL_ID_AQUI pelo ID real se necessário)
        // private const string channelRssUrl = "https://www.youtube.com/feeds/videos.xml?channel_id=CHANNEL_ID_AQUI";
        
        public static async Task<YouTubeVideoList> GetLatestVideos()
        {
            using var request = UnityWebRequest.Get(channelRssUrl);
            var send = request.SendWebRequest();
            await TaskUtility.WaitUntil(() => send.isDone);
            
            if (!Application.isPlaying)
                return null;

            if (request.result == UnityWebRequest.Result.Success)
            {
                var xmlContent = request.downloadHandler.text;
                return ParseRSSFeed(xmlContent);
            }
            else
            {
                Debug.LogError($"Failed to get YouTube videos: {request.error}");
                return null;
            }
        }

        private static YouTubeVideoList ParseRSSFeed(string xmlContent)
        {
            var videoList = new YouTubeVideoList();
            var videos = new List<YouTubeVideo>();

            try
            {
                var xmlDoc = new XmlDocument();
                xmlDoc.LoadXml(xmlContent);

                var entries = xmlDoc.GetElementsByTagName("entry");
                
                foreach (XmlNode entry in entries)
                {
                    var video = new YouTubeVideo();
                    
                    // Título do vídeo
                    var titleNode = entry.SelectSingleNode("title");
                    if (titleNode != null)
                        video.title = titleNode.InnerText;

                    // URL do vídeo
                    var linkNode = entry.SelectSingleNode("link");
                    if (linkNode?.Attributes?["href"] != null)
                        video.url = linkNode.Attributes["href"].Value;

                    // Data de publicação
                    var publishedNode = entry.SelectSingleNode("published");
                    if (publishedNode != null)
                        video.publishedAt = publishedNode.InnerText;

                    // ID do vídeo (extrair da URL)
                    if (!string.IsNullOrEmpty(video.url))
                    {
                        var videoIdStart = video.url.IndexOf("v=") + 2;
                        if (videoIdStart > 1)
                        {
                            var videoIdEnd = video.url.IndexOf("&", videoIdStart);
                            video.videoId = videoIdEnd > 0 
                                ? video.url.Substring(videoIdStart, videoIdEnd - videoIdStart)
                                : video.url.Substring(videoIdStart);
                        }
                    }

                    // Thumbnail (usar thumbnail padrão do YouTube)
                    if (!string.IsNullOrEmpty(video.videoId))
                        video.thumbnailUrl = $"https://img.youtube.com/vi/{video.videoId}/maxresdefault.jpg";

                    videos.Add(video);
                }

                videoList.videos = videos.ToArray();
            }
            catch (Exception e)
            {
                Debug.LogError($"Error parsing YouTube RSS feed: {e.Message}");
            }

            return videoList;
        }
    }

    [Serializable]
    public class YouTubeVideoList
    {
        public YouTubeVideo[] videos;
    }

    [Serializable]
    public class YouTubeVideo
    {
        public string videoId;
        public string title;
        public string url;
        public string thumbnailUrl;
        public string publishedAt;
    }
}
