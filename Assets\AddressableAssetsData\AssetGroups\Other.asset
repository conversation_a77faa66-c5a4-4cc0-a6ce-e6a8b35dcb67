%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bbb281ee3bf0b054c82ac2347e9e782c, type: 3}
  m_Name: Other
  m_EditorClassIdentifier: 
  m_GroupName: Other
  m_GUID: 68af21b94e2bb764b864c60129ab1c8f
  m_SerializeEntries:
  - m_GUID: 36e335017ad71d54fbb10842863188ae
    m_Address: Packages/com.unity.render-pipelines.core/Runtime/RenderPipelineResources/FallbackShader.shader
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: 873ec04b81de2d14c8a493730bb097c7
    m_Address: MaterialCardModelSide
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  - m_GUID: e6e9a19c3678ded42a3bc431ebef7dbd
    m_Address: Packages/com.unity.render-pipelines.universal/Shaders/Utils/FallbackError.shader
    m_ReadOnly: 0
    m_SerializedLabels: []
    FlaggedDuringContentUpdateRestriction: 0
  m_ReadOnly: 0
  m_Settings: {fileID: 11400000, guid: e38330e9272b0a94a916f68bd5115c96, type: 2}
  m_SchemaSet:
    m_Schemas:
    - {fileID: 11400000, guid: fa70848fe31d1e1479c609dff3056c56, type: 2}
    - {fileID: 11400000, guid: fd1a969c686114a4a83d79524ed3659a, type: 2}
