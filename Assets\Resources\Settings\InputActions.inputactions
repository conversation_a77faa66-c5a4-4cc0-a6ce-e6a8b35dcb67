{"name": "InputActions", "maps": [{"name": "UI", "id": "57ad0cc3-45d8-4f98-a70f-eef8d0b3e0a5", "actions": [{"name": "Navigate", "type": "PassThrough", "id": "fb89468a-ad12-4a49-846b-ce51a6c06196", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Submit", "type": "<PERSON><PERSON>", "id": "fe89d163-73ea-4847-93c0-93c643d9b3a2", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Cancel", "type": "<PERSON><PERSON>", "id": "047e8207-c4e8-4588-857b-21d1a360f5f4", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Point", "type": "PassThrough", "id": "73d5cb29-a547-465e-8872-c21fa4692e13", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Click", "type": "PassThrough", "id": "fcb770f5-5f0d-4379-8471-741bc0f2998d", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "MiddleClick", "type": "PassThrough", "id": "32ee4757-3ded-4426-96eb-83ab2eb4f26f", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "RightClick", "type": "PassThrough", "id": "b5569293-a9d6-4705-961b-31020dd2653d", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "MouseScrollWheel", "type": "PassThrough", "id": "4ec088d1-0974-4c2f-9183-8200d9c39b1f", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "LeftScrollWheel", "type": "PassThrough", "id": "1d73407a-c4bc-4553-90d4-3d8e512e607b", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "RightScrollWheel", "type": "PassThrough", "id": "98dc6cd9-d170-4b03-8fb5-bc8bc971e31d", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "TrackedDevicePosition", "type": "PassThrough", "id": "6ab133a6-f90c-4a4b-9ab6-fc3e519e4a0b", "expectedControlType": "Vector3", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "TrackedDeviceOrientation", "type": "PassThrough", "id": "9d517b2e-310d-4b36-bd57-448a20174808", "expectedControlType": "Quaternion", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "MousePos", "type": "Value", "id": "51fa3455-eccf-494a-bf60-f4389e572c35", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "GamepadButtonWest", "type": "<PERSON><PERSON>", "id": "b8f6ac3c-7de9-4896-98a4-3928e09a49b1", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "GamepadButtonNorth", "type": "<PERSON><PERSON>", "id": "73b34d04-0828-4758-b9d9-309c8dad6447", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "LeftStickPress", "type": "<PERSON><PERSON>", "id": "f4d8c485-d109-4622-bceb-6f980920181b", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "RightStickPress", "type": "<PERSON><PERSON>", "id": "0b1813c1-b680-41cf-a081-0e5b660a1828", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "LeftShoulderPress", "type": "<PERSON><PERSON>", "id": "bec3b7d3-144f-4ff2-9eac-3078de235603", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "RightShoulderPress", "type": "<PERSON><PERSON>", "id": "9d6a945a-7418-4fa9-b9c9-36aff75ec472", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "LeftTriggerPress", "type": "<PERSON><PERSON>", "id": "cd85021d-4ca6-426d-944c-a571a171d3bf", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "RightTriggerPress", "type": "<PERSON><PERSON>", "id": "c5a001d4-e8c1-4ba8-aeda-16f81ac0aeb9", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "GamepadSelect", "type": "<PERSON><PERSON>", "id": "75b59e9a-8410-4fc8-808c-9631aa9b90d1", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "GamepadStart", "type": "<PERSON><PERSON>", "id": "9ff91a16-f7d9-4cb9-ab93-6b928f36b67d", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}], "bindings": [{"name": "", "id": "fb8277d4-c5cd-4663-9dc7-ee3f0b506d90", "path": "<Gamepad>/dpad", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": false}, {"name": "Joystick", "id": "e25d9774-381c-4a61-b47c-7b6b299ad9f9", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "3db53b26-6601-41be-9887-63ac74e79d19", "path": "<Joystick>/stick/up", "interactions": "", "processors": "", "groups": "Joystick", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "0cb3e13e-3d90-4178-8ae6-d9c5501d653f", "path": "<Joystick>/stick/down", "interactions": "", "processors": "", "groups": "Joystick", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "0392d399-f6dd-4c82-8062-c1e9c0d34835", "path": "<Joystick>/stick/left", "interactions": "", "processors": "", "groups": "Joystick", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "942a66d9-d42f-43d6-8d70-ecb4ba5363bc", "path": "<Joystick>/stick/right", "interactions": "", "processors": "", "groups": "Joystick", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "Keyboard", "id": "ff527021-f211-4c02-933e-5976594c46ed", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "563fbfdd-0f09-408d-aa75-8642c4f08ef0", "path": "<Keyboard>/w", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "up", "id": "eb480147-c587-4a33-85ed-eb0ab9942c43", "path": "<Keyboard>/upArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "2bf42165-60bc-42ca-8072-8c13ab40239b", "path": "<Keyboard>/s", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "85d264ad-e0a0-4565-b7ff-1a37edde51ac", "path": "<Keyboard>/downArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "74214943-c580-44e4-98eb-ad7eebe17902", "path": "<Keyboard>/a", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "cea9b045-a000-445b-95b8-0c171af70a3b", "path": "<Keyboard>/leftArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "8607c725-d935-4808-84b1-8354e29bab63", "path": "<Keyboard>/d", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "4cda81dc-9edd-4e03-9d7c-a71a14345d0b", "path": "<Keyboard>/rightArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "9e92bb26-7e3b-4ec4-b06b-3c8f8e498ddc", "path": "*/{Submit}", "interactions": "", "processors": "", "groups": "Keyboard&Mouse;Gamepad;Touch;Joystick;XR", "action": "Submit", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "82627dcc-3b13-4ba9-841d-e4b746d6553e", "path": "*/{Cancel}", "interactions": "", "processors": "", "groups": "Keyboard&Mouse;Gamepad;Touch;Joystick;XR", "action": "Cancel", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "c52c8e0b-8179-41d3-b8a1-d149033bbe86", "path": "<Mouse>/position", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Point", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "e1394cbc-336e-44ce-9ea8-6007ed6193f7", "path": "<Pen>/position", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Point", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "5693e57a-238a-46ed-b5ae-e64e6e574302", "path": "<Touchscreen>/touch*/position", "interactions": "", "processors": "", "groups": "Touch", "action": "Point", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "4faf7dc9-b979-4210-aa8c-e808e1ef89f5", "path": "<Mouse>/leftButton", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Click", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "8d66d5ba-88d7-48e6-b1cd-198bbfef7ace", "path": "<Pen>/tip", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Click", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "47c2a644-3ebc-4dae-a106-589b7ca75b59", "path": "<Touchscreen>/touch*/press", "interactions": "", "processors": "", "groups": "Touch", "action": "Click", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "bb9e6b34-44bf-4381-ac63-5aa15d19f677", "path": "<XRController>/trigger", "interactions": "", "processors": "", "groups": "XR", "action": "Click", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "b1a51086-239a-4f8d-96da-f60ada77a283", "path": "<Mouse>/scroll", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "MouseScrollWheel", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "b94b8efb-2550-4433-b4dd-e89cb5caeadd", "path": "<Gamepad>/rightStick", "interactions": "", "processors": "", "groups": "Gamepad;Joystick", "action": "RightScrollWheel", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "24066f69-da47-44f3-a07e-0015fb02eb2e", "path": "<Mouse>/middleButton", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "MiddleClick", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "4c191405-5738-4d4b-a523-c6a301dbf754", "path": "<Mouse>/rightButton", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "RightClick", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "7236c0d9-6ca3-47cf-a6ee-a97f5b59ea77", "path": "<XRController>/devicePosition", "interactions": "", "processors": "", "groups": "XR", "action": "TrackedDevicePosition", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "23e01e3a-f935-4948-8d8b-9bcac77714fb", "path": "<XRController>/deviceRotation", "interactions": "", "processors": "", "groups": "XR", "action": "TrackedDeviceOrientation", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "01448b0e-164d-47da-ac71-1c432ded17ce", "path": "<Mouse>/position", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "MousePos", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "eb543f69-a86a-4bf8-8933-693012db4c95", "path": "<Touchscreen>/position", "interactions": "", "processors": "", "groups": ";Touch", "action": "MousePos", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "283ca2a5-2c3b-414e-83c2-001543383fb2", "path": "<Gamepad>/buttonWest", "interactions": "", "processors": "", "groups": ";Gamepad;Joystick", "action": "GamepadButtonWest", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "848af8b9-c68c-4690-bf0e-a32d587973f5", "path": "<Gamepad>/buttonNorth", "interactions": "", "processors": "", "groups": ";Gamepad;Joystick", "action": "GamepadButtonNorth", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "94752363-14be-4903-ba81-e1e4b7ab6ba4", "path": "<Gamepad>/leftStick", "interactions": "", "processors": "", "groups": ";Gamepad;Joystick", "action": "LeftScrollWheel", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "5d9e05d4-768c-4117-8c48-555497890f3b", "path": "<Gamepad>/leftStickPress", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "LeftStickPress", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "526ce10a-8e1e-41dc-97f5-61b1aa8d9141", "path": "<Gamepad>/rightStickPress", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "RightStickPress", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "96c2ef98-ed0a-411e-9385-0fe18d408650", "path": "<Gamepad>/leftShoulder", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "LeftShoulderPress", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "be17d9da-9211-470e-b6c4-ae15ff3fe5c8", "path": "<Gamepad>/rightShoulder", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "RightShoulderPress", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "143c6bac-91ed-4a38-b54c-91b310fb7d83", "path": "<Gamepad>/leftTrigger", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "LeftTriggerPress", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "f3a965c6-a33f-487c-9917-8349e9b5c956", "path": "<Gamepad>/rightTrigger", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "RightTriggerPress", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "bc028110-4f75-442d-a3e9-fb48fb3564cb", "path": "<Gamepad>/select", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "GamepadSelect", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "c2f9c92a-95e0-4713-8b50-5fabf87886b4", "path": "<Gamepad>/start", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "GamepadStart", "isComposite": false, "isPartOfComposite": false}]}], "controlSchemes": [{"name": "Keyboard&Mouse", "bindingGroup": "Keyboard&Mouse", "devices": [{"devicePath": "<Keyboard>", "isOptional": false, "isOR": false}, {"devicePath": "<Mouse>", "isOptional": false, "isOR": false}]}, {"name": "Gamepad", "bindingGroup": "Gamepad", "devices": [{"devicePath": "<Gamepad>", "isOptional": false, "isOR": false}]}, {"name": "Touch", "bindingGroup": "Touch", "devices": [{"devicePath": "<Touchscreen>", "isOptional": false, "isOR": false}]}, {"name": "Joystick", "bindingGroup": "Joystick", "devices": [{"devicePath": "<Joystick>", "isOptional": false, "isOR": false}]}, {"name": "XR", "bindingGroup": "XR", "devices": [{"devicePath": "<XRController>", "isOptional": false, "isOR": false}]}]}