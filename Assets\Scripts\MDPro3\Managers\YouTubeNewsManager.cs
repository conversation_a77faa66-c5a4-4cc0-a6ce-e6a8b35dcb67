using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;
using MDPro3.Net;
using MDPro3;
using MDPro3.Utility;

public class YouTubeNewsManager : Manager
{
    [Header("YouTube Settings")]
    public string channelId = "UCEt5aRStSkByaZjpSy3BKdA"; // Tenshimaru channel
    
    [Header("UI Elements")]
    public YouTubeChannel youtubeChannel;
    public RawImage videoPic;
    public RawImage videoPic2;
    public Text videoText;
    public Text videoText2;
    public Text textCount;
    public bool showing;

    private List<Texture2D> videoPics = new List<Texture2D>();
    private float width = 455f;
    private int maxLoad = 5;

    private void Start()
    {
        Hide();
        width = GetComponent<RectTransform>().rect.width;
    }

    float idleTime;
    private void Update()
    {
        if (!showing)
            return;
        if (!Program.instance.menu.showing)
            return;
        idleTime += Time.deltaTime;
        if (idleTime > 5f)
            OnRight(0.2f);
    }

    int GetMax()
    {
        if(youtubeChannel == null || youtubeChannel.videos == null)
            return 0;
        return youtubeChannel.videos.Length > maxLoad ? maxLoad : youtubeChannel.videos.Length;
    }

    public void Show()
    {
        if (youtubeChannel == null || youtubeChannel.videos == null || videoPics.Count == 0)
            return;
        var cg = GetComponent<CanvasGroup>();
        cg.alpha = 1.0f;
        cg.blocksRaycasts = true;

        showing = true;

        videoPic.texture = videoPics[0];
        videoText.text = youtubeChannel.videos[0].title;
        videoPic.rectTransform.anchoredPosition = new Vector2(0, 0);
        videoPic2.rectTransform.anchoredPosition = new Vector2(-width, 0);

        currentVideoIndex = 0;
        textCount.text = $"{currentVideoIndex + 1}/{GetMax()}";
    }

    public void Hide()
    {
        var cg = GetComponent<CanvasGroup>();
        cg.alpha = 0f;
        cg.blocksRaycasts = false;

        showing = false;
    }

    public void LoadVideos()
    {
        if (youtubeChannel == null)
            return;
        _ = LoadVideoThumbnailsAsync();
    }

    async Task LoadVideoThumbnailsAsync()
    {
        Hide();
        for (int i = 0; i < youtubeChannel.videos.Length && i < maxLoad; i++)
        {
            var load = Tools.DownloadImageAsync(youtubeChannel.videos[i].thumbnailUrl);

            await TaskUtility.WaitUntil(() => load.IsCompleted);
            if (!Application.isPlaying)
                return;

            videoPics.Add(load.Result);
            if (i == 0)
                Show();
        }
    }

    int currentVideoIndex = 0;
    public void OnRight(float moveTime = 0.1f)
    {
        idleTime = 0f;
        if (youtubeChannel.videos.Length < 2)
            return;

        var next = (currentVideoIndex + 1) % GetMax();

        if (videoPics.Count < currentVideoIndex + 1)
            videoPic.texture = null;
        else
            videoPic.texture = videoPics[currentVideoIndex];
        videoText.text = youtubeChannel.videos[currentVideoIndex].title;

        if (videoPics.Count < next + 1)
            videoPic2.texture = null;
        else
            videoPic2.texture = videoPics[next];
        videoText2.text = youtubeChannel.videos[next].title;

        currentVideoIndex = next;
        textCount.text = $"{currentVideoIndex + 1}/{GetMax()}";

        videoPic.transform.localPosition = new Vector2 (0, 0);
        videoPic2.transform.localPosition = new Vector2(width, 0);

        videoPic.transform.DOLocalMoveX(-width, moveTime);
        videoPic2.transform.DOLocalMoveX(0, moveTime);
    }

    public void OnLeft(float moveTime = 0.1f)
    {
        idleTime = 0f;
        if (youtubeChannel.videos.Length < 2)
            return;
        var max = GetMax();
        var next = (max + currentVideoIndex - 1) % max;

        if (videoPics.Count < currentVideoIndex + 1)
            videoPic.texture = null;
        else
            videoPic.texture = videoPics[currentVideoIndex];

        videoText.text = youtubeChannel.videos[currentVideoIndex].title;
        if(videoPics.Count < next + 1)
            videoPic2.texture = null;
        else
            videoPic2.texture = videoPics[next];
        videoText2.text = youtubeChannel.videos[next].title;
        currentVideoIndex = next;
        textCount.text = $"{currentVideoIndex + 1}/{GetMax()}";

        videoPic.transform.localPosition = new Vector2(0, 0);
        videoPic2.transform.localPosition = new Vector2(-width, 0);

        videoPic.transform.DOLocalMoveX(width, moveTime);
        videoPic2.transform.DOLocalMoveX(0, moveTime);
    }

    public void OnVideoClick()
    {
        if (youtubeChannel != null && youtubeChannel.videos != null && currentVideoIndex < youtubeChannel.videos.Length)
        {
            Application.OpenURL(youtubeChannel.videos[currentVideoIndex].url);
        }
    }

    public void OnClose()
    {
        Hide();
    }
}
